<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add item_type field to cart_mods table
        Schema::table('cart_mods', function (Blueprint $table) {
            $table->string('item_type')->nullable()->after('quantity');
        });

        // Add item_type field to ordereditems_tabs table
        Schema::table('ordereditems_tabs', function (Blueprint $table) {
            $table->string('item_type')->nullable()->after('price');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cart_mods', function (Blueprint $table) {
            $table->dropColumn('item_type');
        });

        Schema::table('ordereditems_tabs', function (Blueprint $table) {
            $table->dropColumn('item_type');
        });
    }
};
