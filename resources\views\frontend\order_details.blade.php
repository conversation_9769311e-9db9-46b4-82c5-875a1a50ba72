@extends('layouts.master')

@section('header_css')
<style>
    .dashboard_stats_wrap {
        text-align: center;
        padding: 2rem;
        border-radius: 0.5rem;
        background: #f5f5f5;
        margin-bottom: 1rem;
    }
    
    .sts_count {
        font-size: 2rem;
    }
    
    .theme-bg {
        background: #0d6efd;
    }
    
    .bg-purple {
        background: #6f42c1;
    }
    
    .order-items {
        border: 1px solid #eee;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 5px;
    }
    
    .item-image img {
        max-width: 80px;
        border-radius: 5px;
    }
</style>
@endsection

@section('content')
<!-- ======================= Top Breadcrubms ======================== -->
<div class="gray py-3">
    <div class="container">
        <div class="row">
            <div class="colxl-12 col-lg-12 col-md-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{route('home_page')}}">
                            @if (session('lang_ben'))
                                হোম
                            @elseif (session('lang_fin'))
                                Etusivu
                            @else
                                Home
                            @endif
                        </a></li>
                        <li class="breadcrumb-item"><a href="{{route('customer.dashboard')}}">
                            @if (session('lang_ben'))
                                ড্যাশবোর্ড
                            @elseif (session('lang_fin'))
                                Hallintapaneeli
                            @else
                                Dashboard
                            @endif
                        </a></li>
                        <li class="breadcrumb-item active" aria-current="page">
                            @if (session('lang_ben'))
                                অর্ডার বিবরণ
                            @elseif (session('lang_fin'))
                                Tilauksen tiedot
                            @else
                                Order Details
                            @endif
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<!-- ======================= Top Breadcrubms ======================== -->

<!-- ======================= Dashboard Detail ======================== -->
<section class="middle">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="sec_title position-relative text-center mb-5">
                    <h2 class="off_title">
                        @if (session('lang_ben'))
                            অর্ডার বিবরণ
                        @elseif (session('lang_fin'))
                            Tilauksen tiedot
                        @else
                            Order Details
                        @endif
                    </h2>
                    <h3 class="ft-bold pt-3">
                        @if (session('lang_ben'))
                            অর্ডার আইডি: #{{$order->id}}
                        @elseif (session('lang_fin'))
                            Tilaus ID: #{{$order->id}}
                        @else
                            Order ID: #{{$order->id}}
                        @endif
                    </h3>
                </div>
            </div>
        </div>

        <div class="row align-items-start justify-content-between">
            <div class="col-12 col-md-12 col-lg-4 col-xl-4 text-center miliods">
                <div class="d-block border rounded mfliud-bot">
                    <div class="dashboard_author px-2 pt-5 pb-4">
                        <div class="dash_auth_thumb circle p-1 border d-inline-flex mx-auto mb-2">
                            @if ($cust_info->prof_pic)
                                <img src="{{asset('uploads/customer')}}/{{$cust_info->prof_pic}}" class="img-fluid circle" width="100" alt="Customer" />
                            @else
                                <img src="{{asset('assets/img/customer.png')}}" class="img-fluid circle" width="100" alt="Customer" />
                            @endif
                        </div>
                        <div class="dash_caption">
                            <h4 class="fs-md ft-medium mb-0 lh-1">{{$cust_info->name}}</h4>
                            @if ($cust_info->city)
                                <span class="text-muted smalls d-block pt-3">{{$cust_info->relto_city->name}}</span>
                            @else
                                <span class="text-muted smalls d-block pt-3" style="font-style: italic">(-- City --)</span>    
                            @endif
                            @if ($cust_info->country)
                                <span class="text-muted smalls d-block">{{$cust_info->relto_country->name}}</span>
                            @else
                                <span class="text-muted smalls d-block" style="font-style: italic">(-- Country --)</span>    
                            @endif
                        </div>
                    </div>
                    
                    <div class="dashboard_author">
                        <div class="d-sm-flex align-items-center justify-content-between mb-2">
                            <h1 class="h6 mb-0 text-gray-900">
                                @if (session('lang_ben'))
                                    ড্যাশবোর্ড নেভিগেশন
                                @elseif (session('lang_fin'))
                                    Hallintapaneelin navigointi
                                @else
                                    Dashboard Navigation
                                @endif
                            </h1>
                        </div>
                        
                        <div class="mb-4 d-flex justify-content-around">
                            <a href="{{route('customer.dashboard')}}" class="btn stretched-link borders text-dark">
                                @if (session('lang_ben'))
                                    আমার অর্ডার
                                @elseif (session('lang_fin'))
                                    Tilaukseni
                                @else
                                    My Orders
                                @endif
                            </a>
                            <a href="{{route('customer.wishlist')}}" class="btn stretched-link borders text-dark">
                                @if (session('lang_ben'))
                                    উইশলিস্ট
                                @elseif (session('lang_fin'))
                                    Toivelista
                                @else
                                    Wishlist
                                @endif
                            </a>
                            <a href="{{route('customer.profile')}}" class="btn stretched-link borders text-dark">
                                @if (session('lang_ben'))
                                    প্রোফাইল তথ্য
                                @elseif (session('lang_fin'))
                                    Profiilitiedot
                                @else
                                    Profile Info
                                @endif
                            </a>
                            <form action="{{route('customer.logout')}}" method="GET">
                                <button type="submit" class="btn stretched-link borders text-dark">
                                    @if (session('lang_ben'))
                                        লগআউট করুন
                                    @elseif (session('lang_fin'))
                                        Kirjaudu ulos
                                    @else
                                        Log Out
                                    @endif
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-md-12 col-lg-8 col-xl-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h4>
                            @if (session('lang_ben'))
                                অর্ডার তথ্য
                            @elseif (session('lang_fin'))
                                Tilaustiedot
                            @else
                                Order Information
                            @endif
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>
                                    @if (session('lang_ben'))
                                        অর্ডার আইডি:
                                    @elseif (session('lang_fin'))
                                        Tilaus ID:
                                    @else
                                        Order ID:
                                    @endif
                                </strong> #{{$order->id}}</p>
                                <p><strong>
                                    @if (session('lang_ben'))
                                        অর্ডারের তারিখ:
                                    @elseif (session('lang_fin'))
                                        Tilauspäivä:
                                    @else
                                        Order Date:
                                    @endif
                                </strong> {{$order->created_at->format('d M Y')}}</p>
                                <p><strong>
                                    @if (session('lang_ben'))
                                        স্ট্যাটাস:
                                    @elseif (session('lang_fin'))
                                        Tila:
                                    @else
                                        Status:
                                    @endif
                                </strong> 
                                <span class="badge 
                                    @if ($order->status == 'Placed')
                                    bg-warning
                                    @elseif ($order->status == 'Packaging')
                                    bg-info
                                    @elseif ($order->status == 'Shipped')
                                    bg-success
                                    @elseif ($order->status == 'Cancelled')
                                    bg-danger
                                    @else
                                    bg-dark
                                    @endif
                                ">
                                @if ($order->status == 'Placed')
                                    @if (session('lang_ben'))
                                        অর্ডার করা হয়েছে
                                    @elseif (session('lang_fin'))
                                        Tilattu
                                    @else
                                        Placed
                                    @endif
                                @elseif ($order->status == 'Packaging')
                                    @if (session('lang_ben'))
                                        প্যাকেজিং
                                    @elseif (session('lang_fin'))
                                        Käsittelyssä
                                    @else
                                        Packaging
                                    @endif
                                @elseif ($order->status == 'Shipped')
                                    @if (session('lang_ben'))
                                        পাঠানো হয়েছে
                                    @elseif (session('lang_fin'))
                                        Toimitettu
                                    @else
                                        Shipped
                                    @endif
                                @elseif ($order->status == 'Delivered')
                                    @if (session('lang_ben'))
                                        পৌঁছে দেওয়া হয়েছে
                                    @elseif (session('lang_fin'))
                                        Perillä
                                    @else
                                        Delivered
                                    @endif
                                @elseif ($order->status == 'Cancelled')
                                    @if (session('lang_ben'))
                                        বাতিল হয়েছে
                                    @elseif (session('lang_fin'))
                                        Peruutettu
                                    @else
                                        Cancelled
                                    @endif
                                @else
                                    {{$order->status}}
                                @endif
                                </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>
                                    @if (session('lang_ben'))
                                        পেমেন্ট মেথড:
                                    @elseif (session('lang_fin'))
                                        Maksutapa:
                                    @else
                                        Payment Method:
                                    @endif
                                </strong> {{$order->payment_method}}</p>
                                <p><strong>
                                    @if (session('lang_ben'))
                                        সাবটোটাল:
                                    @elseif (session('lang_fin'))
                                        Välisumma:
                                    @else
                                        Subtotal:
                                    @endif
                                </strong> ৳{{$order->sub_total}}</p>
                                <p><strong>
                                    @if (session('lang_ben'))
                                        মোট:
                                    @elseif (session('lang_fin'))
                                        Yhteensä:
                                    @else
                                        Total:
                                    @endif
                                </strong> ৳{{$order->grand_total}}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h4>
                            @if (session('lang_ben'))
                                অর্ডার আইটেম
                            @elseif (session('lang_fin'))
                                Tilatut tuotteet
                            @else
                                Order Items
                            @endif
                        </h4>
                    </div>
                    <div class="card-body">
                        @foreach($order_items as $item)
                        <div class="row order-items align-items-center">
                            <div class="col-md-2 col-3 item-image">
                                <img src="{{asset('uploads/product/preview')}}/{{$item->relto_product->preview}}" alt="Product Image" class="img-fluid">
                            </div>
                            <div class="col-md-6 col-9">
                                <h6>{{$item->relto_product->product_name}}</h6>
                                @if($item->color_id)
                                <p class="m-0"><small>Color: {{$item->relto_color->color_name}}</small></p>
                                @endif
                                @if($item->size_id)
                                <p class="m-0"><small>Size: {{$item->relto_size->size}}</small></p>
                                @endif
                                @if($item->item_type)
                                <p class="m-0"><small>Item Type: {{$item->item_type}}</small></p>
                                @endif
                                @if($item->customer_picture)
                                <div class="mt-2">
                                    <small><strong>Your Uploaded Picture:</strong></small><br>
                                    <img src="{{asset('uploads/customer_pictures')}}/{{$item->customer_picture}}"
                                         alt="Your Picture"
                                         style="max-width: 100px; max-height: 100px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;"
                                         onclick="window.open(this.src, '_blank')">
                                    <br><small class="text-muted">Click to view full size</small>
                                </div>
                                @endif
                            </div>
                            <div class="col-md-2 col-6 text-md-center mt-2 mt-md-0">
                                <span>{{$item->quantity}} x ৳{{$item->price}}</span>
                            </div>
                            <div class="col-md-2 col-6 text-end mt-2 mt-md-0">
                                <span class="fw-bold">৳{{$item->quantity * $item->price}}</span>
                            </div>
                        </div>
                        @endforeach

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row justify-content-end">
                                            <div class="col-md-6">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td class="text-end">
                                                            @if (session('lang_ben'))
                                                                সাবটোটাল:
                                                            @elseif (session('lang_fin'))
                                                                Välisumma:
                                                            @else
                                                                Subtotal:
                                                            @endif
                                                        </td>
                                                        <td class="text-end">৳{{$order->sub_total}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-end">
                                                            @if (session('lang_ben'))
                                                                ডেলিভারি চার্জ:
                                                            @elseif (session('lang_fin'))
                                                                Toimituskulut:
                                                            @else
                                                                Delivery Charge:
                                                            @endif
                                                        </td>
                                                        <td class="text-end">৳{{$order->delivery_charge}}</td>
                                                    </tr>
                                                    @if($order->discount)
                                                    <tr>
                                                        <td class="text-end">
                                                            @if (session('lang_ben'))
                                                                ডিসকাউন্ট:
                                                            @elseif (session('lang_fin'))
                                                                Alennus:
                                                            @else
                                                                Discount:
                                                            @endif
                                                        </td>
                                                        <td class="text-end">৳{{$order->discount}}</td>
                                                    </tr>
                                                    @endif
                                                    <tr>
                                                        <td class="text-end fw-bold">
                                                            @if (session('lang_ben'))
                                                                মোট:
                                                            @elseif (session('lang_fin'))
                                                                Yhteensä:
                                                            @else
                                                                Total:
                                                            @endif
                                                        </td>
                                                        <td class="text-end fw-bold">৳{{$order->grand_total}}</td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <a href="{{route('customer.dashboard')}}" class="btn btn-primary">
                            @if (session('lang_ben'))
                                ড্যাশবোর্ডে ফিরে যান
                            @elseif (session('lang_fin'))
                                Takaisin hallintapaneeliin
                            @else
                                Back to Dashboard
                            @endif
                        </a>
                        <a href="{{route('order.inv', $order->id)}}" class="btn btn-success">
                            @if (session('lang_ben'))
                                ইনভয়েস ডাউনলোড করুন
                            @elseif (session('lang_fin'))
                                Lataa lasku
                            @else
                                Download Invoice
                            @endif
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ======================= Dashboard Detail ======================== -->
@endsection 