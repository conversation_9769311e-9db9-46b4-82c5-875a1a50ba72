<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Customer;

class OrdereditemsTab extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $fillable = [
        'order_id',
        'customer_id',
        'product_id',
        'color_id',
        'size_id',
        'quantity',
        'price',
        'review',
        'star',
        'item_type',
        'customer_picture'
    ];

    function relto_product(){
        return $this->belongsTo(Product_list::class, 'product_id');
    }
    function relto_size(){
        return $this->belongsTo(Size::class, 'size_id');
    }
    function relto_color(){
        return $this->belongsTo(Color::class, 'color_id');
    }
    function relto_cust()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }
}
