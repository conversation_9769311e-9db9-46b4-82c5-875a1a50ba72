<?php
// Test script to verify stickers implementation
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Testing Stickers Implementation ===\n\n";

// Check if migration has run
try {
    $hasItemTypeColumn = Schema::hasColumn('cart_mods', 'item_type');
    echo "Cart table has item_type column: " . ($hasItemTypeColumn ? 'YES' : 'NO') . "\n";
    
    $hasOrderItemTypeColumn = Schema::hasColumn('ordereditems_tabs', 'item_type');
    echo "Order items table has item_type column: " . ($hasOrderItemTypeColumn ? 'YES' : 'NO') . "\n";
} catch (Exception $e) {
    echo "Error checking columns: " . $e->getMessage() . "\n";
}

// Check for stickers subcategory
try {
    $stickerSubcategory = App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->first();
    if ($stickerSubcategory) {
        echo "Stickers subcategory found: " . $stickerSubcategory->sub_cata_name . " (ID: " . $stickerSubcategory->id . ")\n";
        echo "Category: " . $stickerSubcategory->relto_cata->cata_name . "\n";
    } else {
        echo "No stickers subcategory found\n";
        
        // List all subcategories
        echo "\nAll subcategories:\n";
        $subcategories = App\Models\Subcategory::with('relto_cata')->get();
        foreach($subcategories as $sub) {
            echo "- " . $sub->sub_cata_name . " (Category: " . $sub->relto_cata->cata_name . ", ID: " . $sub->id . ")\n";
        }
    }
} catch (Exception $e) {
    echo "Error checking subcategories: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
