@extends('layouts.master')



@section('header_css')
<style>
    a.cate_menu {
        cursor: pointer;
        transition: .3s;
    }
    .killore--block-link-content img {
        transition: .2s;
    }
    .killore--block-link-content a:hover {
        letter-spacing: 1.5px;
        color:rgba(255, 0, 0, 0.8);
    }
    .killore--block-link-content a:hover .anc_text {
        color:#ff0000cc;
    }
    .killore--block-link-content a:hover img {
        transform: scale(1.2);
    }

    .cats_side_wrap .cata_item {
        border: 1px solid #e9ecef;
        transition: .3s;
    }
    .cats_side_wrap img {
        transition: .2s;
    }
    .cats_side_wrap p {
        transition: .2s;
    }
    .cats_side_wrap a:hover img {
        transform: scale(1.2);
    }
    .cats_side_wrap a:hover .cata_item {
        border: 1px solid #ff0000cc !important;
    }
    .cats_side_wrap a:hover p {
        color: #ff0000cc !important;
    }

    /* Sticker Banner Styles */
    .sticker-banner {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .sticker-banner .banner-content {
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.3);
    }
    /* Custom Button Styles */
    .custom-btn-green {
        background-color: #28a745;
        color: white;
        transition: all 0.3s ease;
    }
    .custom-btn-green:hover {
        background-color: #17a2b8; /* Sky blue */
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Background Slideshow Styles */
    .sticker-banner {
        position: relative;
        overflow: hidden;
        margin-bottom: 20px;
        min-height: 350px;
        background-color: #f0f0f0; /* Fallback color */
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .banner-background-slideshow {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
    }

    .banner-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 1s ease-in-out;
        z-index: 1;
        background-size: cover !important;
        background-position: center center !important;
    }

    .banner-slide.active {
        opacity: 1;
        z-index: 2;
    }

    .sticker-banner .container {
        position: relative;
        z-index: 10;
    }

    .banner-content {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 8px;
    }

    .sticker-banner .banner-image img {
        transition: all 0.3s ease;
    }
    .sticker-banner:hover .banner-image img {
        transform: scale(1.25) rotate(5deg);
    }

    @media (min-width: 1200px) {
        .service-cards-container {
            padding-left: 24px !important;
            padding-right: 24px !important;
            max-width: 100% !important;
            width: 100% !important;
        }
        .service-cards-row {
            display: flex !important;
            flex-wrap: nowrap !important;
            justify-content: space-between;
            gap: 16px;
            align-items: stretch;
        }
        .service-cards-row > .col-lg-3 {
            flex: 1 1 0;
            max-width: 20%;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }
        .service-card {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: stretch;
        }
        .service-card-body {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
    }
</style>
@endsection



@section('content')

<!-- Sticker Customization Banner -->
<section class="py-4 sticker-banner">
    <!-- Background Slideshow -->
    <div class="banner-background-slideshow">
        <div class="banner-slide active" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{{asset('assets/img/customize.jpg')}}') no-repeat center center !important; background-size: cover !important;"></div>
        <div class="banner-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{{asset('assets/img/bannerslideshowimage2.jpg')}}') no-repeat center center !important; background-size: cover !important;"></div>
        <div class="banner-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{{asset('assets/img/qwenpagladise.jpg')}}') no-repeat center center !important; background-size: cover !important;"></div>
        <div class="banner-slide active" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{{asset('assets/img/samsungoldstickers.jpg')}}') no-repeat center center !important; background-size: cover !important;"></div>
    </div>

    <div class="container position-relative">
        <div class="row align-items-center py-5">
            <div class="col-lg-8 col-md-12">
                <div class="banner-content text-white p-4">
                    <h2 class="display-4 fw-bold mb-3" style="color:rgb(204, 255, 0);">
                        @if (session('lang_ben'))
                            আপনার ডিভাইস কাস্টমাইজ করুন!
                        @elseif (session('lang_fin'))
                            Mukauta laitteesi!
                        @else
                            Customize Your Device!
                        @endif
                    </h2>
                    <p class="fs-5 mb-4">
                        @if (session('lang_ben'))
                            আমাদের স্টাইলিশ স্টিকার দিয়ে আপনার মোবাইল এবং ল্যাপটপকে অনন্য করে তুলুন। আপনার ডিভাইসকে আপনার মতো করে সাজান!
                        @elseif (session('lang_fin'))
                            Tee puhelimestasi ja kannettavastasi ainutlaatuinen tyylikkäillä tarroillamme. Anna laitteellesi persoonallinen ilme!
                        @else
                            Make your mobile and laptop unique with our stylish stickers. Give your device a personal touch that stands out!
                        @endif
                    </p>
                    @php
                        $stickerSubcategories = App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->get();
                        $subcategoryIds = $stickerSubcategories->pluck('id')->toArray();
                        $stickerLink = !empty($subcategoryIds) ? route('shop_page') . '?subcate=' . implode(',', $subcategoryIds) : route('shop_page');
                    @endphp
                    <a href="{{$stickerLink}}" class="btn btn-lg px-4 py-2 fw-bold custom-btn-green">
                        @if (session('lang_ben'))
                            এখনই কাস্টমাইজ করুন <i class="fas fa-arrow-right ms-2"></i>
                        @elseif (session('lang_fin'))
                            Mukauta nyt <i class="fas fa-arrow-right ms-2"></i>
                        @else
                            Customize Now <i class="fas fa-arrow-right ms-2"></i>
                        @endif
                    </a>
                </div>
            </div>
            <div class="col-lg-4 d-none d-lg-block">
                <div class="banner-image text-center">
                    <img src="{{asset('assets/img/sticker-icon.png')}}" alt="Customization Stickers" class="img-fluid" style="max-height: 200px; filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));">
                </div>
            </div>
        </div>
    </div>
</section>


<!-- ======================= Category & Slider ======================== -->
<section class="p-0">
    <div class="container">
        <div class="row">

            <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12">
                <div class="killore-new-block-link border mb-3 mt-3">
                    <div class="px-3 py-3 ft-medium fs-md text-dark gray">
                        @if (session('lang_fra'))
                            Nos principaux services
                        @elseif (session('lang_ben'))
                            আমাদের শীর্ষ পরিষেবা
                        @elseif (session('lang_fin'))
                            Parhaat palvelumme
                        @else
                            Our top services
                        @endif
                    </div>

                    <div class="killore--block-link-content">
                        <ul>
                            <li>
                                @php
                                    $stickerSubcategoriesMenu = App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->get();
                                    $subcategoryIdsMenu = $stickerSubcategoriesMenu->pluck('id')->toArray();
                                    $stickerLinkMenu = !empty($subcategoryIdsMenu) ? route('shop_page') . '?subcate=' . implode(',', $subcategoryIdsMenu) : route('shop_page');
                                @endphp
                                <a class="cate_menu" href="{{$stickerLinkMenu}}"><img width="25" src="{{asset('assets/img/sticker-icon.png')}}" alt="Customization Stickers"> &nbsp;<span class="anc_text">
                                    @if (session('lang_fin'))
                                        Koristelutarrat
                                    @elseif (session('lang_ben'))
                                        কাস্টমাইজেশন স্টিকার
                                    @else
                                        Customization Stickers
                                    @endif
                                </span></a>
                            </li>
                            <li>
                                <a class="cate_menu" href="{{route('repair.service')}}"><img width="25" src="{{asset('assets/img/repair-icon.png')}}" alt="Repair Service"> &nbsp;<span class="anc_text">
                                    @if (session('lang_fin'))
                                        Korjauspalvelu
                                    @elseif (session('lang_ben'))
                                        মেরামত সেবা
                                    @else
                                        Repair Service
                                    @endif
                                </span></a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-xl-9 col-lg-9 col-md-12 col-sm-12">
                <div class="home-slider auto-slider mb-3 mt-3">

                    <!-- Slide -->
                    <div data-background-image="{{asset('assets/img/repairimage.jpg')}}" class="item">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="home-slider-container">

                                        <!-- Slide Title -->
                                        <div class="home-slider-desc">
                                            <div class="home-slider-title mb-4">
                                                <h5 class="fs-sm ft-ragular mb-2">
                                                    @if (session('lang_ben'))
                                                        মেরামত সেবা
                                                    @elseif (session('lang_fin'))
                                                        Korjauspalvelu
                                                    @else
                                                        Repair Service
                                                    @endif
                                                </h5>
                                                <h1 class="mb-2 ft-bold">
                                                    @if (session('lang_ben'))
                                                        সস্তা<br>নির্ভরযোগ্য <span class="theme-cl">মেরামত</span>
                                                    @elseif (session('lang_fin'))
                                                        Edullisempi<br>Luotettava <span class="theme-cl">Korjaus</span>
                                                    @else
                                                        Cheaper<br>Reliable <span class="theme-cl">Repairing</span>
                                                    @endif
                                                </h1>
                                                <span class="trending">
                                                    @if (session('lang_ben'))
                                                        আমরা আপনাকে সেরা মূল্যে আপনার<br> ইলেকট্রনিক্স মেরামত করতে সাহায্য করি
                                                    @elseif (session('lang_fin'))
                                                        Autamme sinua korjaamaan elektroniikkasi<br> parhaaseen hintaan
                                                    @else
                                                        We help you fix your electronics at best value
                                                    @endif
                                                </span>
                                            </div>

                                            <a href="{{route('repair.service')}}" class="btn btn-white stretched-link hover-black">
                                                @if (session('lang_ben'))
                                                    কোটেশন <i class="lni lni-arrow-right ml-2"></i>
                                                @elseif (session('lang_fin'))
                                                    Tarjous <i class="lni lni-arrow-right ml-2"></i>
                                                @else
                                                    Quote <i class="lni lni-arrow-right ml-2"></i>
                                                @endif
                                            </a>
                                        </div>
                                        <!-- Slide Title / End -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Slide Stickers -->
                    <div data-background-image="{{asset('assets/img/customize.jpg')}}" class="item">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="home-slider-container">

                                        <!-- Slide Title -->
                                        <div class="home-slider-desc">
                                            <div class="home-slider-title mb-4">
                                                <h5 class="fs-sm ft-ragular mb-2">
                                                    @if (session('lang_ben'))
                                                    কাস্টমাইজেশন স্টিকার
                                                    @elseif (session('lang_fin'))
                                                    Mukautustarrat
                                                    @else
                                                        Customization Stickers
                                                    @endif
                                                </h5>
                                                <h1 class="mb-2 ft-bold">
                                                    @if (session('lang_ben'))
                                                    একে নিজের মতো করে নিন<br><span class="text-success">স্টিকার</span> দি
                                                    @elseif (session('lang_fin'))
                                                    Tee siitä omasi<br>Valitse <span class="text-success">Tarrat</span>
                                                    @else
                                                        Make it yours<br>With <span class="text-success">Stickers</span>
                                                    @endif
                                                </h1>
                                                <span class="trending">
                                                    @if (session('lang_ben'))
                                                    ইলেকট্রনিক্স কাস্টমাইজেশন
                                                    @elseif (session('lang_fin'))
                                                    Mukauta elektroniset laitteesi
                                                    @else
                                                    Customize Your Electronics Devices
                                                    @endif
                                                </span>
                                            </div>

                                            <a href="{{$stickerLink}}" class="btn btn-white stretched-link hover-black">
                                                @if (session('lang_ben'))
                                                এক্ষুনি দেখুন<i class="lni lni-arrow-right ml-2"></i>
                                                @elseif (session('lang_fin'))
                                                Tutustu<i class="lni lni-arrow-right ml-2"></i>
                                                @else
                                                    Explore<i class="lni lni-arrow-right ml-2"></i>
                                                @endif
                                            </a>
                                        </div>
                                        <!-- Slide Title / End -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>




                    <!-- Slide -->
                    <div data-background-image="{{asset('assets/img/shoppintcargdeal.jpg')}}" class="item">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="home-slider-container">

                                        <!-- Slide Title -->
                                        <div class="home-slider-desc">
                                            <div class="home-slider-title mb-4">
                                                <h5 class="fs-sm ft-ragular mb-2">
                                                    @if (session('lang_ben'))
                                                        দুর্দান্ত অফার
                                                    @elseif (session('lang_fin'))
                                                        Hyviä Tarjouksia
                                                    @else
                                                        Great Deals
                                                    @endif
                                                </h5>
                                                <h1 class="mb-2 ft-bold">
                                                    @if (session('lang_ben'))
                                                        আপনার পছন্দের পণ্য কিনুন<br><span class="text-success">ছাড়ের মূল্যে</span>
                                                    @elseif (session('lang_fin'))
                                                        Osta suosikkituotteesi<br><span class="text-success">Alennettuun Hintaan</span>
                                                    @else
                                                        Buy your favourites<br>At <span class="text-success">Discounted Price</span>
                                                    @endif
                                                </h1>
                                                <span class="trending">
                                                    @if (session('lang_ben'))
                                                        আমরা আপনাকে সেরা মূল্যের গ্যারান্টি দিচ্ছি
                                                    @elseif (session('lang_fin'))
                                                        Lupaamme sinulle parhaan mahdollisen tarjouksen
                                                    @else
                                                        We promise you to give the best deal you can find
                                                    @endif
                                                </span>
                                            </div>

                                            <a href="{{route('shop_page')}}" class="btn btn-white stretched-link hover-black">
                                                @if (session('lang_ben'))
                                                    এখনই কিনুন<i class="lni lni-arrow-right ml-2"></i>
                                                @elseif (session('lang_fin'))
                                                    Osta Nyt<i class="lni lni-arrow-right ml-2"></i>
                                                @else
                                                    Shop Now<i class="lni lni-arrow-right ml-2"></i>
                                                @endif
                                            </a>
                                        </div>
                                        <!-- Slide Title / End -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide -->
                    <div data-background-image="{{asset('assets/img/')}}" class="item">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="home-slider-container">

                                        <!-- Slide Title -->
                                        <div class="home-slider-desc">
                                            <div class="home-slider-title mb-4">
                                                <h5 class="fs-sm ft-ragular mb-2">
                                                    @if (session('lang_ben'))
                                                        আমাদের সম্পর্কে
                                                    @elseif (session('lang_fin'))
                                                        Tietoa Meistä
                                                    @else
                                                        About Us
                                                    @endif
                                                </h5>
                                                <h1 class="mb-2 ft-bold">
                                                    @if (session('lang_ben'))
                                                        পরিচিত হন<br>আপনার বিশ্বস্ত অংশীদারের সাথে
                                                    @elseif (session('lang_fin'))
                                                        Tutustu<br>Luotettavaan kumppaniisi
                                                    @else
                                                        Be familiar<br>With your trusted partner
                                                    @endif
                                                </h1>
                                                <span class="trending">
                                                    @if (session('lang_ben'))
                                                        আমরা অভিজ্ঞ পেশাদারদের একটি দল যারা আমাদের গ্রাহকদের সর্বোত্তম সম্ভাব্য সেবা প্রদান করতে প্রতিশ্রুতিবদ্ধ, সস্তা এবং নির্ভরযোগ্য উপায়ে
                                                    @elseif (session('lang_fin'))
                                                        Olemme kokeneiden ammattilaisten tiimi, joka on omistautunut tarjoamaan parasta mahdollista palvelua asiakkaillemme, edullisemmin ja luotettavasti
                                                    @else
                                                        We are a team of experienced professionals who are dedicated to providing the best possible service to our customers, cheaper and in a reliable way
                                                    @endif
                                                </span>
                                            </div>

                                            <a href="{{route('about_page')}}" class="btn theme-bg text-light">
                                                @if (session('lang_ben'))
                                                    আমাদের সম্পর্কে জানুন<i class="lni lni-arrow-right ml-2"></i>
                                                @elseif (session('lang_fin'))
                                                    Tutustu meihin<i class="lni lni-arrow-right ml-2"></i>
                                                @else
                                                    Get to know us<i class="lni lni-arrow-right ml-2"></i>
                                                @endif
                                            </a>
                                        </div>
                                        <!-- Slide Title / End -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </div>
</section>
<!-- ======================= Category & Slider ======================== -->

<!-- ======================= Service Cards ======================== -->
<section class="py-4">
    <div class="container service-cards-container">
        <div class="row service-cards-row">
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="service-card">
                    <div class="service-card-img">
                        <img src="{{asset('assets/img/repair-icon.png')}}" alt="Repair Service">
                    </div>
                    <div class="service-card-body">
                        <h3 class="service-card-title">
                            @if (session('lang_ben'))
                                মেরামত সেবা
                            @elseif (session('lang_fin'))
                                Korjauspalvelu
                            @else
                                Repair Service
                            @endif
                        </h3>
                        <p class="service-card-text">
                            @if (session('lang_ben'))
                                আমরা আপনাকে সেরা মূল্যে আপনার ইলেকট্রনিক্স মেরামত করতে সাহায্য করি
                            @elseif (session('lang_fin'))
                                Autamme sinua korjaamaan elektroniikkasi parhaaseen hintaan
                            @else
                                We help you fix your electronics at the best value
                            @endif
                        </p>
                        <a href="{{route('repair.service')}}" class="service-card-link">
                            @if (session('lang_ben'))
                                আরও জানুন
                            @elseif (session('lang_fin'))
                                Lue lisää
                            @else
                                Learn More
                            @endif
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="service-card">
                    <div class="service-card-img">
                        <img src="{{asset('assets/img/sticker-icon.png')}}" alt="Customization Stickers">
                    </div>
                    <div class="service-card-body">
                        <h3 class="service-card-title">
                            @if (session('lang_ben'))
                                কাস্টমাইজেশন স্টিকার
                            @elseif (session('lang_fin'))
                                Koristelutarrat
                            @else
                                Customization Stickers
                            @endif
                        </h3>
                        <p class="service-card-text">
                            @if (session('lang_ben'))
                                স্টাইলিশ স্টিকার দিয়ে আপনার ইলেকট্রনিক ডিভাইসগুলি কাস্টমাইজ করুন
                            @elseif (session('lang_fin'))
                                Mukauta elektroniset laitteesi tyylikkäillä tarroilla
                            @else
                                Customize your electronic devices with stylish stickers
                            @endif
                        </p>
                        @php
                            $stickerSubcategoriesService = App\Models\Subcategory::where('sub_cata_name', 'LIKE', '%sticker%')->get();
                            $subcategoryIdsService = $stickerSubcategoriesService->pluck('id')->toArray();
                            $stickerLinkService = !empty($subcategoryIdsService) ? route('shop_page') . '?subcate=' . implode(',', $subcategoryIdsService) : route('shop_page');
                        @endphp
                        <a href="{{$stickerLinkService}}" class="service-card-link">
                            @if (session('lang_ben'))
                                আরও জানুন
                            @elseif (session('lang_fin'))
                                Lue lisää
                            @else
                                Learn More
                            @endif
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="service-card">
                    <div class="service-card-img">
                        <img src="{{asset('assets/img/shop-icon.png')}}" alt="Shop Products">
                    </div>
                    <div class="service-card-body">
                        <h3 class="service-card-title">
                            @if (session('lang_ben'))
                                পণ্য কিনুন
                            @elseif (session('lang_fin'))
                                Osta tuotteita
                            @else
                                Shop Products
                            @endif
                        </h3>
                        <p class="service-card-text">
                            @if (session('lang_ben'))
                                আমাদের বিস্তৃত ইলেকট্রনিক্স পণ্য এবং অ্যাক্সেসরিজ অন্বেষণ করুন
                            @elseif (session('lang_fin'))
                                Tutustu laajaan elektroniikka- ja lisävarusteiden valikoimaamme
                            @else
                                Explore our wide range of electronics products and accessories
                            @endif
                        </p>
                        <a href="{{route('shop_page')}}" class="service-card-link">
                            @if (session('lang_ben'))
                                এখনই কিনুন
                            @elseif (session('lang_fin'))
                                Osta nyt
                            @else
                                Shop Now
                            @endif
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="service-card">
                    <div class="service-card-img">
                        <img src="{{asset('assets/img/pwa-icon.png')}}" alt="PWA App">
                    </div>
                    <div class="service-card-body">
                        <h3 class="service-card-title">
                            @if (session('lang_ben'))
                                মোবাইল অ্যাপ
                            @elseif (session('lang_fin'))
                                Mobiilisovellus
                            @else
                                Mobile App
                            @endif
                        </h3>
                        <p class="service-card-text">
                            @if (session('lang_ben'))
                                আমাদের PWA অ্যাপ ডাউনলোড করুন এবং যেকোনো সময়, যেকোনো জায়গা থেকে কেনাকাটা করুন
                            @elseif (session('lang_fin'))
                                Lataa PWA-sovelluksemme ja osta missä tahansa, milloin tahansa
                            @else
                                Download our PWA app and shop anytime, anywhere
                            @endif
                        </p>
                        <a href="{{route('pwa.install.guide')}}" class="service-card-link">
                            @if (session('lang_ben'))
                                এখনই ডাউনলোড করুন
                            @elseif (session('lang_fin'))
                                Lataa nyt
                            @else
                                Install Now
                            @endif
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="service-card">
                    <div class="service-card-img">
                        <img src="{{asset('assets/img/about-icon.png')}}" alt="About Us">
                    </div>
                    <div class="service-card-body">
                        <h3 class="service-card-title">
                            @if (session('lang_ben'))
                                আমাদের সম্পর্কে
                            @elseif (session('lang_fin'))
                                Tietoa meistä
                            @else
                                About Us
                            @endif
                        </h3>
                        <p class="service-card-text">
                            @if (session('lang_ben'))
                                আমরা কে এবং আমরা কীভাবে আপনাকে সাহায্য করতে পারি তা জানুন
                            @elseif (session('lang_fin'))
                                Opi tuntemaan keitä olemme ja miten voimme auttaa sinua
                            @else
                                Learn who we are and how we can help you
                            @endif
                        </p>
                        <a href="{{route('about_page')}}" class="service-card-link">
                            @if (session('lang_ben'))
                                আরও জানুন
                            @elseif (session('lang_fin'))
                                Lue lisää
                            @else
                                Learn More
                            @endif
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ======================= Service Cards End ======================== -->





<!-- ======================= Modern Product List ======================== -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="text-center mb-5">
                    <h6 class="text-uppercase text-primary fw-bold mb-2">
                        @if (session('lang_ben'))
                            জনপ্রিয় পণ্য
                        @elseif (session('lang_fin'))
                            Suositut Tuotteet
                        @else
                            Trendy Products
                        @endif
                    </h6>
                    <h2 class="fw-bold">
                        @if (session('lang_ben'))
                            আমাদের ট্রেন্ডিং পণ্য
                        @elseif (session('lang_fin'))
                            Suosituimmat tuotteemme
                        @else
                            Our Trending Products
                        @endif
                    </h2>
                </div>
            </div>
        </div>

        <div class="row">
            @foreach ($product_all as $product)
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-4">
                    <div class="modern-product-card">
                        <!-- Product Badges -->
                        @if ($product->discount != 0)
                            <div class="modern-product-badge sale">
                                -{{$product->discount}}%
                            </div>
                        @endif

                        @foreach (App\Models\Inventory::where('product_id', $product->id)->get() as $inv_upd)
                            @if($inv_upd->created_at != null && Carbon\carbon::now()->diffInDays($inv_upd->created_at) < 30)
                                <div class="modern-product-badge new" style="left: auto; right: 10px;">
                                    @if (session('lang_ben'))
                                        নতুন
                                    @elseif (session('lang_fin'))
                                        Uusi
                                    @else
                                        New
                                    @endif
                                </div>
                            @endif
                        @endforeach

                        <!-- Out of Stock Badge -->
                        @php
                            $total_qty = 0;
                        @endphp

                        @foreach (App\Models\Inventory::where('product_id', $product->id)->get() as $inv_upd)
                            @php
                                $total_qty += $inv_upd->quantity;
                            @endphp
                        @endforeach

                        @if($total_qty == 0)
                            <div class="modern-product-badge" style="background-color: #dc3545; left: auto; right: 10px; top: 45px;">
                                @if (session('lang_ben'))
                                    স্টক শেষ
                                @elseif (session('lang_fin'))
                                    Loppuunmyyty
                                @else
                                    Out of Stock
                                @endif
                            </div>
                        @endif

                        <!-- Product Image -->
                        <div class="modern-product-img">
                            <a href="{{route('product.details', $product->slug)}}">
                                <img src="{{asset('uploads/product/preview')}}/{{$product->preview}}" alt="{{$product->product_name}}">
                            </a>

                            <!-- Product Actions -->
                            <div class="modern-product-actions">
                                <form action="" method="POST" class="d-inline">
                                    @csrf
                                    <input type="hidden" name="product_id" value="{{$product->id}}">

                                    @if (App\Models\WishTable::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $product->id)->count() >= 1)
                                        <a class="btn" href="{{route('wishlist.remove.btn', $product->id)}}">
                                            <i class="fas fa-heart text-danger"></i>
                                        </a>
                                    @else
                                        <button class="btn" type="submit" formaction="{{route('wishlist.store')}}">
                                            <i class="far fa-heart"></i>
                                        </button>
                                    @endif
                                </form>

                                <a href="{{route('product.details', $product->slug)}}" class="btn">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Product Content -->
                        <div class="modern-product-content">
                            <div class="modern-product-category">{{$product->relto_cata->cata_name}}</div>
                            <h3 class="modern-product-title">
                                <a href="{{route('product.details', $product->slug)}}">{{$product->product_name}}</a>
                            </h3>

                            <!-- Product Rating -->
                            <div class="modern-product-rating">
                                @php
                                    $avg_star = App\Models\OrdereditemsTab::where('product_id', $product->id)->avg('star');
                                @endphp

                                @for ($i = 1; $i <= $avg_star; $i++)
                                    <i class="fas fa-star"></i>
                                @endfor

                                @if ($avg_star - floor($avg_star) > 0)
                                    <i class="fas fa-star-half-alt"></i>
                                    @php $i++; @endphp
                                @endif

                                @for (; $i <= 5; $i++)
                                    <i class="far fa-star"></i>
                                @endfor

                                <span>({{App\Models\OrdereditemsTab::where('product_id', $product->id)->whereNotNull('review')->count()}})</span>
                            </div>

                            <!-- Product Price -->
                            <div class="modern-product-price">
                                @if ($product->discount != 0)
                                    <span class="old-price">{{$product->price}}€</span>
                                    <span class="regular-price">{{number_format($product->after_disc)}}€</span>
                                @else
                                    <span class="regular-price">{{number_format($product->price)}}€</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="{{route('shop_page')}}" class="btn btn-primary px-4 py-2">
                    @if (session('lang_ben'))
                        আরও অন্বেষণ করুন
                    @elseif (session('lang_fin'))
                        Tutki lisää
                    @else
                        Explore More
                    @endif
                    <i class="fas fa-arrow-right ms-2"></i>
                </a>
            </div>
        </div>
    </div>
</section>
<!-- ======================= Modern Product List End ======================== -->


<!-- ======================= Brand Start ============================ -->

<!--
<section class="py-3 br-top">
    <div class="container-fluid">
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12">
                <div class="smart-brand">

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-1.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-2.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-3.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-4.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-5.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-6.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-1.png" class="img-fluid" alt="" />
                    </div>

                    <div class="single-brnads">
                        <img src="assets/img/shop-logo-2.png" class="img-fluid" alt="" />
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>

-->
<!-- ======================= Brand Start ============================ -->

<!-- ======================= Tag Wrap Start ============================ -->

<!--
<section class="bg-cover" style="background:url({{asset('assets/img/e-middle-banner.png')}}) no-repeat;">
    <div class="ht-60"></div>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10 col-sm-12">
                <div class="tags_explore text-center">
                    <h2 class="mb-0 text-white ft-bold">
                        @if (session('lang_ben'))
                            ৭০% পর্যন্ত বড় ছাড়
                        @elseif (session('lang_fin'))
                            Suuri Ale Jopa 70% Pois
                        @else
                            Big Sale Up To 70% Off
                        @endif
                    </h2>
                    <p class="text-light fs-lg mb-4">
                        @if (session('lang_ben'))
                            সীমিত সময়ের জন্য বিশেষ অফার
                        @elseif (session('lang_fin'))
                            Rajoitetun ajan erikoistarjoukset
                        @else
                            Exclussive Offers For Limited Time
                        @endif
                    </p><p>
                    <a href="{{route('coupon.view')}}" class="btn btn-lg bg-white px-5 text-dark ft-medium">
                        @if (session('lang_ben'))
                            আপনার অর্ডার অন্বেষণ করুন
                        @elseif (session('lang_fin'))
                            Tutustu tilaukseen
                        @else
                            Explore Your Order
                        @endif
                    </a>
                </p></div>
            </div>
        </div>
    </div>
    <div class="ht-60"></div>
</section>

-->
<!-- ======================= Tag Wrap Start ============================ -->

<!-- ======================= All Category ======================== -->
<section class="middle">
    <div class="container">

        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="sec_title position-relative text-center">
                    <h2 class="off_title">
                        @if (session('lang_ben'))
                            সকল বিভাগ
                        @elseif (session('lang_fin'))
                            Kaikki kategoriat
                        @else
                            All Categories
                        @endif
                    </h2>
                    <h3 class="ft-bold pt-3">
                        @if (session('lang_ben'))
                            আমাদের বিভাগসমূহ
                        @elseif (session('lang_fin'))
                            Kategoriamme
                        @else
                            Our Categories
                        @endif
                    </h3>
                </div>
            </div>
        </div>

        <div class="row align-items-center justify-content-center">

            @foreach ($cata_all_in as $cata)
                <div class="col-xl-2 col-lg-2 col-md-3 col-sm-6 col-4">
                    <div class="cats_side_wrap text-center mx-auto mb-3">
                        <a data-custom-value="{{$cata->id}}" class="cate_menu">
                            <div class="sl_cat_01">
                                <div class="d-inline-flex align-items-center justify-content-center p-4 circle mb-2 cata_item">
                                    <div class="d-block"><img src="{{asset('uploads/category')}}/{{$cata->cata_image}}" class="img-fluid" width="40" alt=""></div>
                                </div>
                            </div>
                            <div class="sl_cat_02">
                                <h6 class="m-0 ft-medium fs-sm">
                                    <p>
                                        @if (session('lang_fin'))
                                            {{ $cata->cata_name_fin ?? $cata->cata_name }}
                                        @elseif (session('lang_ben'))
                                            {{ $cata->cata_name_ben ?? $cata->cata_name }}
                                        @else
                                            {{ $cata->cata_name }}
                                        @endif
                                    </p>
                                </h6>
                            </div>
                        </a>
                    </div>
                </div>
            @endforeach


        </div>

    </div>
</section>
<!-- ======================= All Category ======================== -->

<!-- ======================= Modern Customer Reviews ======================== -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="text-center mb-5">
                    <h6 class="text-uppercase text-primary fw-bold mb-2">
                        @if (session('lang_ben'))
                            প্রশংসাপত্র
                        @elseif (session('lang_fin'))
                            Suositukset
                        @else
                            Testimonials
                        @endif
                    </h6>
                    <h2 class="fw-bold">
                        @if (session('lang_ben'))
                            গ্রাহক প্রতিক্রিয়া
                        @elseif (session('lang_fin'))
                            Asiakkaiden arviot
                        @else
                            Client Reviews
                        @endif
                    </h2>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Modern Testimonial 1 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="modern-testimonial">
                    <div class="modern-testimonial-header">
                        <div class="modern-testimonial-avatar">
                            <img src="{{asset('assets/img/team-1.jpg')}}" alt="Mark Jevenue">
                        </div>
                        <div class="modern-testimonial-author">
                            <h4 class="modern-testimonial-name">Mark Jevenue</h4>
                            <p class="modern-testimonial-title">Helsinki, Finland</p>
                        </div>
                    </div>
                    <div class="modern-testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="modern-testimonial-content">
                        I got a new smartphone from iPac Telecom and I'm very pleased with the experience. The phone is fast and thin, exactly what I wanted. Their service was perfect with friendly staff to make the process smooth – it was quite amazing!
                    </div>
                </div>
            </div>

            <!-- Modern Testimonial 2 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="modern-testimonial">
                    <div class="modern-testimonial-header">
                        <div class="modern-testimonial-avatar">
                            <img src="{{asset('assets/img/pori01.jpg')}}" alt="Henna Virtanen">
                        </div>
                        <div class="modern-testimonial-author">
                            <h4 class="modern-testimonial-name">Henna Virtanen</h4>
                            <p class="modern-testimonial-title">Tampere, Finland</p>
                        </div>
                    </div>
                    <div class="modern-testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <div class="modern-testimonial-content">
                        Ostin tämän tuotteen viime viikolla, ja olen erittäin tyytyväinen hankintaani. Toimitus oli nopea ja paketti saapui täydellisesti pakattuna. Tuote vastasi kuvausta, ja laatu ylitti odotukseni. Suosittelen ehdottomasti!
                    </div>
                </div>
            </div>

            <!-- Modern Testimonial 3 -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="modern-testimonial">
                    <div class="modern-testimonial-header">
                        <div class="modern-testimonial-avatar">
                            <img src="{{asset('assets/img/team-3.jpg')}}" alt="Freddie Robinhood">
                        </div>
                        <div class="modern-testimonial-author">
                            <h4 class="modern-testimonial-name">Mikko Korhonen</h4>
                            <p class="modern-testimonial-title">Turku, Finland</p>
                        </div>
                    </div>
                    <div class="modern-testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="modern-testimonial-content">
                        Recently I purchased a smartphone from iPac Telecom and I am very satisfied with it! The phone is fast and elegant and is everything I was seeking for. Their support was responsive, and the whole experience was enjoyable!
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ======================= Modern Customer Reviews End ======================== -->

<!-- ======================= Top Seller Start, FEAtured, Recent added ============================ -->

<!-- ======================= Top Seller End ============================ -->
@endsection



@section('footer_script')

{{-- === Cate Search === --}}
<script>
    $('.cate_menu').click(function(){
        var cate_id = $(this).data('custom-value');

        var search_link = "{{route('shop_page')}}" + "?cate=" + cate_id;
        window.location.href = search_link;
    });
</script>

{{-- === Banner Background Slideshow === --}}
<script>
    $(document).ready(function() {
        // Background slideshow functionality
        var slides = $('.banner-slide');
        var currentSlide = 0;
        var slideInterval = 2000; // 2 seconds

        // Function to cycle through slides
        function nextSlide() {
            // Fade out current slide
            slides.eq(currentSlide).css('opacity', 0);
            slides.eq(currentSlide).removeClass('active');

            // Move to next slide
            currentSlide = (currentSlide + 1) % slides.length;

            // Fade in next slide
            slides.eq(currentSlide).addClass('active');
            slides.eq(currentSlide).css('opacity', 1);
        }

        // Make sure first slide is visible
        slides.css('opacity', 0);
        slides.eq(0).css('opacity', 1);
        slides.eq(0).addClass('active');

        // Start the slideshow
        setInterval(nextSlide, slideInterval);
    });
</script>
@endsection
